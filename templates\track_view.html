<!DOCTYPE html>
<html>
<head>
    <title>设备定位展示</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: #f5f8fa;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 100%;
            height: 100vh;
            background: #fff;
            display: flex;
            flex-direction: column;
        }
        .header {
            background-color: #34495e;
            color: white;
            padding: 12px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .back-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            transition: background 0.3s;
        }
        .back-link:hover {
            background: rgba(255,255,255,0.3);
        }
        .control-panel {
            background: #fff;
            padding: 16px 24px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        .btn-primary {
            background: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background: #2980b9;
        }
        .map-container {
            flex: 1;
            position: relative;
            overflow: hidden;
        }
        #mapDiv {
            width: 100%;
            height: 100%;
        }
        .info-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255,255,255,0.95);
            padding: 12px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            min-width: 250px;
            z-index: 1000;
        }
        .info-panel h4 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 14px;
        }
        .info-item {
            margin-bottom: 6px;
            font-size: 13px;
            color: #666;
        }
        .error-message {
            color: #e74c3c;
            background: #fdf2f2;
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 4px solid #e74c3c;
            margin: 8px 0;
        }
        .success-message {
            color: #27ae60;
            background: #f0f9f4;
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 4px solid #27ae60;
            margin: 8px 0;
        }
        .no-map {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
            font-size: 16px;
        }
    </style>
    <!-- 引入天地图API - 使用浏览器端密钥 -->
    <script type="text/javascript" src="https://api.tianditu.gov.cn/api?v=4.0&tk=46697f7c9051544d0a2c159dfcedb425"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>设备定位展示</h1>
            <a class="back-link" href="/">← 返回主页</a>
        </div>

        <div class="control-panel">
            <button class="btn btn-primary" onclick="refreshLocation()">刷新位置</button>
            <span id="statusText">正在获取最新位置数据...</span>
            <div id="rawDataDisplay" style="margin-left: 20px; font-family: monospace; font-size: 12px; color: #666;"></div>
            <div id="messageArea"></div>
        </div>

        <div class="map-container">
            <div id="mapDiv"></div>
        </div>
    </div>

    <script>
        let map;
        let marker;
        let updateInterval;

        // 初始化天地图
        function initMap() {
            try {
                console.log('开始初始化天地图...');

                // 创建地图实例
                map = new T.Map('mapDiv');

                // 设置地图中心点和缩放级别（18级约为100米视角）
                const center = new T.LngLat(116.40969, 39.89945);
                map.centerAndZoom(center, 18);

                // 添加地图类型控件
                const mapTypeCtrl = new T.Control.MapType();
                map.addControl(mapTypeCtrl);

                // 添加缩放控件
                const zoomCtrl = new T.Control.Zoom();
                map.addControl(zoomCtrl);

                // 添加比例尺控件
                const scaleCtrl = new T.Control.Scale();
                map.addControl(scaleCtrl);

                console.log('天地图初始化成功');
                document.getElementById('statusText').textContent = '地图已加载，正在获取位置数据...';

                // 测试地图是否正常工作
                setTimeout(() => {
                    console.log('地图对象:', map);
                    console.log('地图容器:', document.getElementById('mapDiv'));
                }, 1000);

            } catch (error) {
                console.error('天地图初始化失败:', error);
                document.getElementById('statusText').textContent = '地图加载失败: ' + error.message;
                initBackupMap();
            }
        }

        // 备用地图方案 - 使用简单的OpenStreetMap
        function initBackupMap() {
            try {
                const mapDiv = document.getElementById('mapDiv');
                mapDiv.innerHTML = `
                    <div style="width: 100%; height: 100%; background: #f0f0f0; display: flex; align-items: center; justify-content: center; flex-direction: column;">
                        <h3>地图服务暂时不可用</h3>
                        <p>正在显示设备位置信息</p>
                        <div id="locationDisplay" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); margin-top: 20px;">
                            <h4>设备位置</h4>
                            <div id="locationInfo">等待位置数据...</div>
                        </div>
                    </div>
                `;
                document.getElementById('statusText').textContent = '使用备用显示方案';
            } catch (error) {
                console.error('备用地图初始化也失败:', error);
            }
        }

        // 获取最新位置数据
        async function getLatestLocation() {
            try {
                const response = await fetch('/api/latest_location');
                const result = await response.json();

                if (result.status === 'success' && result.data) {
                    updateLocationDisplay(result.data);
                    updateMapLocation(result.data);
                    document.getElementById('statusText').textContent = '位置数据已更新';
                } else {
                    document.getElementById('statusText').textContent = '暂无位置数据';
                    clearLocationDisplay();
                }
            } catch (error) {
                console.error('获取位置数据失败:', error);
                document.getElementById('statusText').textContent = '获取位置数据失败';
                showMessage('获取位置数据失败', 'error');
            }
        }

        // 更新地图上的位置标记
        function updateMapLocation(data) {
            if (!data.longitude || !data.latitude) {
                console.log('无效的位置数据');
                return;
            }

            console.log('更新地图位置:', data.longitude, data.latitude);

            // 检查天地图是否可用
            if (map && typeof T !== 'undefined') {
                try {
                    // 创建位置点
                    const lngLat = new T.LngLat(data.longitude, data.latitude);
                    console.log('创建位置点:', lngLat);

                    // 移除旧的标记
                    if (marker) {
                        map.removeOverLay(marker);
                        console.log('移除旧标记');
                    }

                    // 创建红色圆点标记
                    const icon = new T.Icon({
                        iconUrl: 'data:image/svg+xml;base64,' + btoa(`
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                <circle cx="8" cy="8" r="6" fill="#e74c3c" stroke="white" stroke-width="2"/>
                            </svg>
                        `),
                        iconSize: new T.Point(16, 16),
                        iconAnchor: new T.Point(8, 8)
                    });

                    // 创建新的标记
                    marker = new T.Marker(lngLat, {icon: icon});
                    console.log('创建红色标记');

                    // 添加标记到地图
                    map.addOverLay(marker);
                    console.log('添加标记到地图');

                    // 只设置地图中心，不改变缩放级别
                    map.panTo(lngLat);
                    console.log('设置地图中心（保持当前缩放级别）');

                    console.log('天地图位置更新成功');

                } catch (error) {
                    console.error('更新天地图位置失败:', error);
                    updateBackupLocation(data);
                }
            } else {
                console.log('天地图不可用，使用备用方案');
                updateBackupLocation(data);
            }
        }

        // 备用位置显示
        function updateBackupLocation(data) {
            const locationInfo = document.getElementById('locationInfo');
            if (locationInfo) {
                locationInfo.innerHTML = `
                    <div style="text-align: left; line-height: 1.6;">
                        <p><strong>经度:</strong> ${data.longitude}</p>
                        <p><strong>纬度:</strong> ${data.latitude}</p>
                        <p><strong>时间:</strong> ${data.time}</p>
                        <p><strong>GPS状态:</strong> ${data.gps_status}</p>
                        <p><strong>ICCID:</strong> ${data.iccid}</p>
                        <hr style="margin: 10px 0;">
                        <p><strong>在线地图查看:</strong></p>
                        <a href="https://api.map.baidu.com/marker?location=${data.latitude},${data.longitude}&title=设备位置&content=ICCID:${data.iccid}&output=html" target="_blank" style="color: #3498db; margin-right: 10px;">百度地图</a>
                        <a href="https://uri.amap.com/marker?position=${data.longitude},${data.latitude}&name=设备位置" target="_blank" style="color: #3498db;">高德地图</a>
                    </div>
                `;
            }
        }

        // 更新位置显示
        function updateLocationDisplay(data) {
            console.log('位置数据更新:', data);

            // 更新状态文本
            document.getElementById('statusText').textContent = '位置数据已更新';

            // 显示原始数据
            const rawDataDiv = document.getElementById('rawDataDisplay');
            if (rawDataDiv && data.raw_data) {
                rawDataDiv.textContent = data.raw_data;
            }
        }

        // 清除位置显示
        function clearLocationDisplay() {
            document.getElementById('statusText').textContent = '暂无位置数据';
            document.getElementById('rawDataDisplay').textContent = '';

            // 清除地图标记
            if (marker && map) {
                map.removeOverLay(marker);
                marker = null;
            }
        }

        // 手动刷新位置
        function refreshLocation() {
            document.getElementById('statusText').textContent = '正在刷新位置数据...';
            getLatestLocation();
        }

        // 启动自动更新
        function startAutoUpdate() {
            // 立即获取一次数据
            getLatestLocation();

            // 每5秒自动更新一次
            updateInterval = setInterval(getLatestLocation, 5000);
        }

        // 停止自动更新
        function stopAutoUpdate() {
            if (updateInterval) {
                clearInterval(updateInterval);
                updateInterval = null;
            }
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const messageArea = document.getElementById('messageArea');
            const className = type === 'error' ? 'error-message' : 'success-message';
            messageArea.innerHTML = `<div class="${className}">${message}</div>`;

            // 3秒后自动清除消息
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 3000);
        }

        // 等待天地图API加载完成
        function waitForTiandituAPI() {
            let attempts = 0;
            const maxAttempts = 10;

            function checkAPI() {
                attempts++;
                console.log(`检查天地图API，第${attempts}次尝试`);

                if (typeof T !== 'undefined') {
                    console.log('天地图API加载成功');
                    initMap();
                    startAutoUpdate();
                } else if (attempts < maxAttempts) {
                    console.log('天地图API未加载，1秒后重试...');
                    setTimeout(checkAPI, 1000);
                } else {
                    console.error('天地图API加载失败，使用备用方案');
                    document.getElementById('statusText').textContent = '天地图API加载失败，使用备用显示';
                    initBackupMap();
                    startAutoUpdate();
                }
            }

            checkAPI();
        }

        // 页面加载完成后初始化
        window.onload = function() {
            console.log('页面加载完成，开始初始化...');
            waitForTiandituAPI();
        };

        // 页面关闭时停止自动更新
        window.addEventListener('beforeunload', function() {
            stopAutoUpdate();
        });
    </script>
</body>
</html>
