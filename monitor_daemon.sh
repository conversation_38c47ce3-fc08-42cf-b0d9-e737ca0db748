#!/bin/bash

# 设备监控服务器守护脚本
# 功能：持续监控服务器状态，自动重启异常停止的服务

# 工作目录
WORK_DIR="/root/device_monitor"
# 进程名称
PROCESS_NAME="device_tcp_server.py"
# 日志文件
LOG_FILE="/var/log/device_monitor_daemon.log"
# 检查间隔（秒）
CHECK_INTERVAL=30

# 记录日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 检查进程是否运行
check_process() {
    pgrep -f "$PROCESS_NAME" > /dev/null
    return $?
}

# 启动服务器
start_server() {
    log_message "正在启动设备监控服务器..."
    cd "$WORK_DIR"

    # 检查必要文件是否存在
    if [ ! -f "$WORK_DIR/start_server.sh" ]; then
        log_message "错误：start_server.sh 文件不存在"
        return 1
    fi

    # 启动服务器（后台运行）
    nohup ./start_server.sh > "$WORK_DIR/server.log" 2>&1 &

    # 等待几秒钟确认启动
    sleep 5

    if check_process; then
        log_message "设备监控服务器启动成功"
        return 0
    else
        log_message "设备监控服务器启动失败"
        return 1
    fi
}

# 停止服务器
stop_server() {
    log_message "正在停止设备监控服务器..."
    pkill -f "$PROCESS_NAME"
    sleep 3

    if check_process; then
        log_message "强制停止设备监控服务器..."
        pkill -9 -f "$PROCESS_NAME"
        sleep 2
    fi

    if ! check_process; then
        log_message "设备监控服务器已停止"
        return 0
    else
        log_message "无法停止设备监控服务器"
        return 1
    fi
}

# 重启服务器
restart_server() {
    log_message "正在重启设备监控服务器..."
    stop_server
    sleep 3
    start_server
}

# 主循环
main_loop() {
    log_message "设备监控守护脚本启动，检查间隔：${CHECK_INTERVAL}秒"

    # 首次启动
    if ! check_process; then
        log_message "检测到服务器未运行，正在启动..."
        start_server
    else
        log_message "检测到服务器已在运行"
    fi

    # 持续监控循环
    while true; do
        sleep "$CHECK_INTERVAL"

        if check_process; then
            # 检查端口是否正常监听
            if netstat -tlnp | grep -q ":48085.*LISTEN" && netstat -tlnp | grep -q ":5000.*LISTEN"; then
                log_message "服务器运行正常 - TCP:48085 Web:5000"
            else
                log_message "警告：服务器进程存在但端口未正常监听，重启服务器"
                restart_server
            fi
        else
            log_message "检测到服务器停止运行，正在重启..."
            start_server
        fi
    done
}

# 信号处理函数
cleanup() {
    log_message "收到退出信号，正在清理..."
    stop_server
    log_message "守护脚本退出"
    exit 0
}

# 注册信号处理
trap cleanup SIGTERM SIGINT

# 检查是否以root用户运行
if [ "$EUID" -ne 0 ]; then
    echo "请以root用户运行此脚本"
    exit 1
fi

# 创建日志文件
touch "$LOG_FILE"

# 根据参数执行不同操作
case "$1" in
    start)
        log_message "手动启动服务器"
        start_server
        ;;
    stop)
        log_message "手动停止服务器"
        stop_server
        ;;
    restart)
        log_message "手动重启服务器"
        restart_server
        ;;
    status)
        if check_process; then
            echo "服务器正在运行"
            ps aux | grep -v grep | grep "$PROCESS_NAME"
        else
            echo "服务器未运行"
        fi
        ;;
    daemon)
        # 守护模式
        main_loop
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|daemon}"
        echo "  start   - 启动服务器"
        echo "  stop    - 停止服务器"
        echo "  restart - 重启服务器"
        echo "  status  - 查看状态"
        echo "  daemon  - 守护模式（持续监控）"
        exit 1
        ;;
esac
