[Unit]
Description=设备监控TCP服务器
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/root/device_monitor
Environment=PATH=/root/device_monitor/venv/bin:/usr/local/bin:/usr/bin:/bin
ExecStart=/root/device_monitor/venv/bin/python3 /root/device_monitor/device_tcp_server.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# 如果服务启动失败，等待10秒后重试
StartLimitInterval=300
StartLimitBurst=5

[Install]
WantedBy=multi-user.target
