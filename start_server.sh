#!/bin/bash

# 设置脚本在任何命令失败时退出
set -e

# 日志文件
LOG_FILE="/var/log/device_monitor.log"

# 记录启动日志
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 正在启动设备监控服务器..." | tee -a "$LOG_FILE"

# 切换到工作目录
cd /root/device_monitor

# 检查虚拟环境是否存在
if [ ! -d "venv" ]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 错误：虚拟环境不存在，请先创建虚拟环境" | tee -a "$LOG_FILE"
    exit 1
fi

# 激活虚拟环境
source venv/bin/activate

# 检查Python脚本是否存在
if [ ! -f "device_tcp_server.py" ]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 错误：device_tcp_server.py 文件不存在" | tee -a "$LOG_FILE"
    exit 1
fi

# 记录启动成功
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 设备监控服务器启动成功" | tee -a "$LOG_FILE"

# 启动Python服务器
python3 device_tcp_server.py
