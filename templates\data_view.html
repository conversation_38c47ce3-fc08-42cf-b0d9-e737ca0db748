<!DOCTYPE html>
<html>
<head>
    <title>原始数据展示</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: "Microsoft YaHei", Arial, sans-serif; background: #f5f8fa; margin: 0; }
        .container { max-width: 1800px; margin: 30px auto; background: #fff; border-radius: 10px; box-shadow: 0 2px 12px #e0e0e0; padding: 36px; }
        h2 { margin-top: 0; }
        .search-bar { margin-bottom: 22px; text-align: center; }
        .search-bar input, .search-bar button { height: 38px; font-size: 18px; }
        .search-bar input { width: 340px; border: 1px solid #ccc; border-radius: 4px; padding: 0 12px; }
        .search-bar button { background: #3498db; color: #fff; border: none; border-radius: 4px; padding: 0 28px; cursor: pointer; margin-left: 12px; font-size: 18px; }
        .search-bar button:hover { background: #217dbb; }
        table { width: 100%; border-collapse: collapse; background: #fff; margin-top: 10px; }
        th, td { padding: 12px 10px; border-bottom: 1px solid #eee; text-align: left; font-size: 17px; }
        th { background: #f8f8f8; color: #333; }
        tr:hover { background: #f0f6fa; }
        td { white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 300px; }
        .iccid-col { min-width: 180px; }
        .content-col { max-width: 1500px; min-width: 900px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
        .time-col { min-width: 160px; }
        .back-link { color: #3498db; text-decoration: underline; cursor: pointer; margin-bottom: 18px; display: inline-block; font-size: 18px; }
        .nodata { text-align: center; color: #888; padding: 30px 0; font-size: 19px; }
    </style>
</head>
<body>
    <div class="container">
        <a class="back-link" href="/">← 返回主页面</a>
        <h2 style="text-align:center;">原始数据展示</h2>
        <div class="search-bar">
            <input type="text" id="ccidInput" placeholder="输入ICCID号进行搜索">
            <button onclick="searchData()">搜索</button>
            <button onclick="resetSearch()">重置</button>
        </div>
        <table id="dataTable">
            <thead>
                <tr>
                    <th class="iccid-col">ICCID</th>
                    <th class="content-col">内容</th>
                    <th class="time-col">时间</th>
                </tr>
            </thead>
            <tbody id="dataTableBody">
                <!-- 数据行 -->
            </tbody>
        </table>
        <div id="nodata" class="nodata" style="display:none;">暂无数据</div>
    </div>
    <script>
        let allData = [];
        function fetchData() {
            fetch('/api/raw_data')
            .then(response => response.json())
            .then(data => {
                allData = data;
                renderTable(allData);
            });
        }
        function parseRaw(row) {
            // 尝试从内容中提取ICCID、设备id
            let iccid = '';
            let deviceid = '';
            let content = row.data;
            let status = '未解析';
            // ICCID: 19位数字
            let iccidMatch = content.match(/(\d{19,20})/);
            if (iccidMatch) iccid = iccidMatch[1];
            // 设备id: S+ 或 B+ 开头的字符串
            let idMatch = content.match(/([SB]\+[^+\s]*)/);
            if (idMatch) deviceid = idMatch[1];
            // 状态: 如果内容包含E结尾，显示未解析，否则可自定义
            if (content.endsWith('E')) status = '未解析';
            return {
                iccid,
                deviceid,
                content,
                time: row.time,
                status
            };
        }
        function renderTable(data) {
            const tbody = document.getElementById('dataTableBody');
            const nodata = document.getElementById('nodata');
            tbody.innerHTML = '';
            // 按上传时间倒序
            data = data.slice().sort((a, b) => b.time.localeCompare(a.time));
            if (data.length === 0) {
                nodata.style.display = '';
                return;
            } else {
                nodata.style.display = 'none';
            }
            for (const row of data) {
                const d = parseRaw(row);
                tbody.innerHTML += `<tr><td class="iccid-col">${d.iccid}</td><td class="content-col" title="${d.content}">${d.content}</td><td class="time-col">${d.time}</td></tr>`;
            }
        }
        function searchData() {
            const ccid = document.getElementById('ccidInput').value.trim();
            if (!ccid) { renderTable(allData); return; }
            const filtered = allData.filter(row => row.data.includes(ccid));
            renderTable(filtered);
        }
        function resetSearch() {
            document.getElementById('ccidInput').value = '';
            renderTable(allData);
        }
        fetchData();
    </script>
</body>
</html>
