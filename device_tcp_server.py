import socket
import threading
import datetime
import logging
import time
from web_monitor import add_device_data, add_log_handler, active_clients, init_tcp_server, add_raw_data

# 创建全局TCP服务器实例
tcp_server_instance = None

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tcp_server.log'),
        logging.StreamHandler()
    ]
)

class DeviceTCPServer:
    def __init__(self, host='0.0.0.0', port=48085):
        self.host = host
        self.port = port
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.is_listening = False  # 新增：监听状态

        # 存储当前有效的配置
        self.interval_config = None    # 间隔配置
        self.work_time_config = None   # 工作时间段配置
        self.time_config = None        # 时间设置配置
        self.pack_config = None        # 数据打包配置

        # 合并配置指令（默认值）
        self.combined_config = "ZL+S30+F00E00+N0"

        # 数据计数器
        self.s_data_count = 0    # S数据计数
        self.b_data_count = 0    # B数据计数
        # 重复数据计数器
        self.repeat_data_count = 0
        self.seen_times = set()

        # 设备连接开关（默认打开）
        self.device_connection_enabled = True

        # 移除配置发送开关，改为自动发送配置

    def set_device_connection_enabled(self, enabled):
        """
        设置设备连接开关，关闭时只关闭监听socket，禁止新设备连接，不断开已连接设备，不清除任何数据。
        """
        self.device_connection_enabled = enabled
        status = "开启" if enabled else "关闭"
        logging.info(f"设备连接开关已{status}")
        if enabled:
            # 如果开启且未监听，则重新绑定端口并监听
            if not self.is_listening:
                try:
                    self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    self.sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                    self.sock.bind((self.host, self.port))
                    self.sock.listen(5)
                    self.is_listening = True
                    logging.info("TCP端口已重新开启监听")
                except Exception as e:
                    logging.error(f"开启监听失败: {str(e)}")
        else:
            # 如果关闭且正在监听，则只关闭socket，不断开已连接设备
            if self.is_listening:
                try:
                    self.sock.close()
                    self.is_listening = False
                    logging.info("TCP端口已关闭，禁止新设备连接，已连接设备不受影响")
                except Exception as e:
                    logging.error(f"关闭监听失败: {str(e)}")

    def get_device_connection_enabled(self):
        """获取设备连接开关状态"""
        return self.device_connection_enabled

    # 移除配置发送开关相关方法

    def get_data_counts(self):
        """获取数据计数"""
        return {
            's_count': self.s_data_count,
            'b_count': self.b_data_count,
            'total_count': self.s_data_count + self.b_data_count
        }

    def get_repeat_data_count(self):
        return self.repeat_data_count

    def reset_repeat_data_count(self):
        self.repeat_data_count = 0
        self.seen_times = set()
        logging.info("重复数据计数器已清零")
        return True

    def reset_data_counts(self):
        """清零数据计数"""
        self.s_data_count = 0
        self.b_data_count = 0
        self.repeat_data_count = 0
        self.seen_times = set()
        logging.info("数据计数器已清零")
        return True

    def set_config_command(self, cmd):
        """设置配置指令"""
        try:
            # 移除前缀"ZL+"，如果有的话
            cmd_content = cmd.replace("ZL+", "")
            full_cmd = f"ZL+{cmd_content}"

            # 检查是否是时间设置指令
            if 'TIME=' in cmd_content:
                time_str = cmd_content.split('TIME=')[1]
                if len(time_str) == 14 and time_str.isdigit():
                    # 时间设置指令不需要调整时区，因为设备会自己处理
                    self.time_config = full_cmd
                    logging.info(f"时间设置指令已更新: {full_cmd}")
                    return True
                else:
                    logging.error(f"时间格式不正确: {time_str}")
                    return False

            # 检查是否为发送间隔配置
            elif cmd_content.startswith('M') or cmd_content.startswith('S'):
                interval_type = cmd_content[0]
                interval_value = cmd_content[1:]

                try:
                    int_value = int(interval_value)
                    if int_value <= 0:
                        logging.error(f"间隔值必须大于0: {interval_value}")
                        return False

                    # 更新间隔配置（替换旧的配置）
                    self.interval_config = full_cmd
                    # 更新合并配置中的间隔部分
                    self.update_combined_config()
                    logging.info(f"间隔配置已更新: {full_cmd}")
                    return True
                except ValueError:
                    logging.error(f"间隔值必须是有效数字: {interval_value}")
                    return False

            # 检查是否为数据打包指令
            elif cmd_content.startswith('N'):
                pack_value = cmd_content[1:]
                try:
                    int_value = int(pack_value)
                    if int_value < 10 or int_value > 100 or int_value % 10 != 0:
                        logging.error(f"数据打包值必须为10-100之间的10的倍数: {pack_value}")
                        return False
                    # 保存数据打包配置
                    self.pack_config = full_cmd
                    # 更新合并配置中的数据打包部分
                    self.update_combined_config()
                    logging.info(f"数据打包配置已更新: {full_cmd}")
                    return True
                except ValueError:
                    logging.error(f"数据打包值必须是有效数字: {pack_value}")
                    return False

            # 检查是否为工作时间段配置
            elif 'F' in cmd_content and 'E' in cmd_content:
                import re
                start_match = re.search(r'F(\d{2})', cmd_content)
                end_match = re.search(r'E(\d{2})', cmd_content)

                if start_match and end_match:
                    # 将输入的北京时间转换为UTC时间
                    start_hour = int(start_match.group(1))
                    end_hour = int(end_match.group(1))

                    # 允许设置00时
                    if start_hour < 0 or start_hour > 23 or end_hour < 0 or end_hour > 23:
                        logging.error(f"工作时间段小时数必须在0-23之间: F{start_hour}E{end_hour}")
                        return False

                    # 转换为UTC时间（-8小时）
                    utc_start = (start_hour - 8) % 24
                    utc_end = (end_hour - 8) % 24

                    # 生成新的配置命令
                    self.work_time_config = f"ZL+F{str(utc_start).zfill(2)}E{str(utc_end).zfill(2)}"
                    # 更新合并配置中的时间段部分
                    self.update_combined_config()
                    logging.info(f"工作时间段配置已更新（UTC）: {self.work_time_config}")
                    return True

            logging.error(f"无法识别的配置命令: {cmd}")
            return False

        except Exception as e:
            logging.error(f"解析配置命令失败: {str(e)}")
            return False

    def update_combined_config(self):
        """更新合并配置指令"""
        # 解析当前合并配置
        parts = self.combined_config.replace("ZL+", "").split("+")

        # 默认值
        interval_part = "S30"
        time_part = "F00E00"
        pack_part = "N0"

        # 更新间隔部分
        if self.interval_config:
            interval_content = self.interval_config.replace("ZL+", "")
            if interval_content.startswith('M') or interval_content.startswith('S'):
                interval_part = interval_content

        # 更新时间段部分
        if self.work_time_config:
            time_content = self.work_time_config.replace("ZL+", "")
            if 'F' in time_content and 'E' in time_content:
                time_part = time_content

        # 更新数据打包部分
        if self.pack_config:
            pack_content = self.pack_config.replace("ZL+", "")
            if pack_content.startswith('N'):
                pack_part = pack_content

        # 重新组合配置指令
        self.combined_config = f"ZL+{interval_part}+{time_part}+{pack_part}"
        logging.info(f"合并配置已更新: {self.combined_config}")

    def get_active_configs(self):
        """获取当前有效的配置列表"""
        configs = []

        # 添加时间配置（如果有）
        if self.time_config:
            configs.append(self.time_config)

        # 添加合并配置指令（包含间隔、时间段、数据打包）
        configs.append(self.combined_config)

        return configs

    def start(self):
        # 启动时先绑定端口并监听
        try:
            self.sock.bind((self.host, self.port))
            self.sock.listen(5)
            self.is_listening = True
            logging.info(f"TCP服务器启动，监听地址: {self.host}:{self.port}")
        except Exception as e:
            logging.error(f"TCP服务器启动失败: {str(e)}")
            self.is_listening = False

        while True:
            # 检查开关状态，未开启则等待
            if not self.device_connection_enabled or not self.is_listening:
                time.sleep(1)
                continue
            try:
                client, address = self.sock.accept()
                logging.info(f"新的连接来自: {address}")
                client_thread = threading.Thread(target=self.handle_client, args=(client, address))
                client_thread.start()
            except Exception as e:
                # 监听socket被关闭时accept会报错，忽略
                time.sleep(1)
                continue

    def handle_client(self, client_socket, address):
        try:
            # 检查设备连接开关
            if not self.device_connection_enabled:
                logging.info(f"设备连接开关已关闭，拒绝连接: {address}")
                client_socket.send("CONNECTION_DENIED\r\n".encode())
                client_socket.close()
                return

            # 记录新连接的设备
            active_clients[address] = client_socket

            # 连接建立后立即发送初始响应
            client_socket.send("\r\n".encode())
            time.sleep(0.1)
            logging.info(f"新的连接来自: {address}")

            # 移除连接后立即发送配置指令的逻辑
            # 改为在收到数据后再发送配置指令

            while True:
                data = client_socket.recv(1024)
                if not data:
                    break

                data_str = data.decode('utf-8', errors='ignore').strip()
                logging.info(f"收到数据: {data_str}")
                add_raw_data(data_str)

                if data_str.startswith('HX+') or data_str.startswith('HB+'):
                    # 处理HX和HB开头的数据
                    self.parse_device_data(data_str)
                    # 收到数据后自动发送ZL配置指令
                    configs = self.get_active_configs()
                    for config in configs:
                        client_socket.send(f"{config}\r\n".encode())
                        logging.info(f"收到数据后发送配置: {config}")
                        time.sleep(0.1)

                    # 清除已发送的时间配置
                    if self.time_config:
                        self.time_config = None

                elif data_str.startswith('HY'):
                    # 处理HY开头的数据
                    self.parse_hy_data(data_str)
                    # 收到数据后自动发送ZL配置指令
                    configs = self.get_active_configs()
                    for config in configs:
                        client_socket.send(f"{config}\r\n".encode())
                        logging.info(f"收到HY数据后发送配置: {config}")
                        time.sleep(0.1)

                    # 清除已发送的时间配置
                    if self.time_config:
                        self.time_config = None

                elif data_str.startswith('TX+'):
                    # TX调试信息只记录日志，不显示在界面上
                    logging.info(f"收到TX调试信息: {data_str}")
                    client_socket.send("OK\r\n".encode())
                    time.sleep(0.1)

                elif data_str.startswith('AT+'):
                    # 处理AT命令
                    logging.info(f"收到AT命令: {data_str}")
                    if 'CIPSEND' in data_str:
                        client_socket.send(">\r\n".encode())
                    else:
                        client_socket.send("OK\r\n".encode())
                    time.sleep(0.1)

                else:
                    if 'RD=' in data_str and 'len=' in data_str:
                        logging.info(f"可能是TX调试信息(无前缀): {data_str}")
                        formatted_debug = f"调试信息(无前缀):\n{data_str}"
                        add_device_data(formatted_debug)

                    client_socket.send("OK\r\n".encode())
                    time.sleep(0.1)

        except Exception as e:
            logging.error(f"处理客户端 {address} 数据时发生错误: {str(e)}")
        finally:
            if address in active_clients:
                del active_clients[address]
            client_socket.close()
            logging.info(f"客户端连接关闭: {address}")

    def parse_hy_data(self, data):
        """解析HY开头的数据"""
        try:
            # 检查数据格式: HY[长度][S/B]+数据+E
            if len(data) < 6:
                logging.error(f"HY数据长度不足: {data}")
                return

            # 找到第一个+号的位置来确定长度和类型字段
            first_plus = data.find('+')
            if first_plus == -1:
                logging.error(f"HY数据格式错误，未找到+号: {data}")
                return

            # 提取长度和类型部分 (例如: HY110S)
            header = data[:first_plus]  # HY110S
            if len(header) < 4:
                logging.error(f"HY数据头部格式错误: {header}")
                return

            # 提取数据类型 (S或B)
            data_type = header[-1]  # S或B
            length_str = header[2:-1]  # 110

            if not length_str.isdigit():
                logging.error(f"HY数据长度格式错误: {length_str}")
                return

            # 验证数据类型
            if data_type not in ['S', 'B']:
                logging.error(f"HY数据类型错误: {data_type}")
                return

            # 检查数据是否以E结尾
            if not data.endswith('E'):
                logging.error(f"HY数据格式错误，未以E结尾: {data}")
                return

            # 提取数据部分（去掉开头的HY110S+和结尾的+E）
            data_content = data[first_plus+1:-2]  # 去掉开头的HY110S+和结尾的+E
            parts = data_content.split('+')

            logging.info(f"HY数据解析: 头部={header}, 类型={data_type}, 字段数={len(parts)}")
            logging.info(f"数据字段: {parts}")

            if len(parts) >= 17:  # HY格式包含17个字段
                data_type_desc = "实时数据" if data_type == 'S' else "历史数据"

                # 增加数据计数
                if data_type == 'S':
                    self.s_data_count += 1
                elif data_type == 'B':
                    self.b_data_count += 1

                # 检查重复数据（用UTC时间字段parts[2]）
                utc_time = parts[2]  # UTC时间
                if utc_time in self.seen_times:
                    self.repeat_data_count += 1
                else:
                    self.seen_times.add(utc_time)

                # 解析UTC时间
                time_str = self.parse_utc_time(utc_time)

                # 格式化数据
                parsed_data = f"""
[{data_type_desc}] HY格式数据:
位置: 经度={parts[0]}, 纬度={parts[1]}
时间: {time_str}
海拔: {parts[3]}m
GPS状态: {parts[4]} ({self.get_gps_status(parts[4])})
卫星数量: {parts[5]}
速度: {parts[6]}节
电池电压: {parts[7]}V
温度: {parts[8]}°C
HDOP: {parts[9]}
PDOP: {parts[10]}
陀螺仪: X={parts[11]}, Y={parts[12]}, Z={parts[13]}
方位角: {parts[14]}
GSM信号: {parts[15]}
ICCID: {parts[16] if len(parts) > 16 else 'N/A'}
"""
                # 将解析后的数据添加到web监控
                add_device_data(parsed_data)
                logging.info(f"解析HY数据:\n{parsed_data}")
            else:
                logging.error(f"HY数据字段不足: {len(parts)} < 17")

        except Exception as e:
            logging.error(f"解析HY数据失败: {str(e)}")

    def parse_device_data(self, data):
        try:
            # 移除头部'HX+'或'HB+'和尾部
            parts = data.replace('HX+', '').replace('HB+', '').split('+')
            if len(parts) >= 16:  # 确保数据完整
                # 提取时间部分并解析
                utc_time = parts[2]  # 格式: 011027.080425
                hour = utc_time[0:2]
                minute = utc_time[2:4]
                second = utc_time[4:6]
                day = utc_time.split('.')[1][0:2]
                month = utc_time.split('.')[1][2:4]
                year = utc_time.split('.')[1][4:6]

                # 转换为北京时间
                beijing_hour = (int(hour) + 8) % 24

                # 处理跨天情况
                from datetime import datetime, timedelta
                base_date = datetime(2000 + int(year), int(month), int(day))
                if int(hour) + 8 >= 24:
                    base_date = base_date + timedelta(days=1)

                # 格式化时间字符串
                time_str = f"{base_date.year}-{str(base_date.month).zfill(2)}-{str(base_date.day).zfill(2)} {str(beijing_hour).zfill(2)}:{minute}:{second}"

                # 格式化其他数据
                parsed_data = f"""
位置: 经度={parts[0]}, 纬度={parts[1]}
时间: {time_str}
海拔: {parts[3]}m
GPS状态: {parts[4]} ({self.get_gps_status(parts[4])})
速度: {parts[5]}节
电池: {int(parts[6])/1000}V
温度: {parts[7]}°C
HDOP: {parts[8]}
PDOP: {parts[9]}
陀螺仪: X={parts[10]}, Y={parts[11]}, Z={parts[12]}
方位角: {parts[13]}
信号强度: {parts[14]}
ICCID: {parts[-1] if parts[-2] == 'E' else None}
"""
                # 将解析后的数据添加到web监控
                add_device_data(parsed_data)
                logging.info(f"解析数据:\n{parsed_data}")
        except Exception as e:
            logging.error(f"解析数据失败: {str(e)}")

    def get_gps_status(self, status):
        """获取GPS状态描述"""
        status_dict = {
            '0': '未定位',
            '1': '高精度定位',
            '2': '2D定位',
            '3': '3D定位',
            '6': '估算值'
        }
        return status_dict.get(status, '未知状态')

    def parse_utc_time(self, utc_str):
        """解析设备发送的UTC时间并转换为北京时间"""
        try:
            if len(utc_str) == 12:  # 格式: HHmmss.DDMMYY
                # 提取时间部分
                hour = int(utc_str[0:2])
                minute = utc_str[2:4]
                second = utc_str[4:6]

                # 提取日期部分（在点号后面）
                date_part = utc_str.split('.')[1]
                day = date_part[0:2]
                month = date_part[2:4]
                year = date_part[4:6]

                # 转换为北京时间（UTC+8）
                beijing_hour = (hour + 8) % 24

                # 如果跨天了，需要调整日期
                day_adjust = 1 if (hour + 8) >= 24 else 0
                if day_adjust:
                    # 创建datetime对象进行日期计算
                    from datetime import datetime, timedelta
                    base_date = datetime(2000 + int(year), int(month), int(day))
                    adjusted_date = base_date + timedelta(days=day_adjust)
                    year = str(adjusted_date.year - 2000).zfill(2)
                    month = str(adjusted_date.month).zfill(2)
                    day = str(adjusted_date.day).zfill(2)

                return f"20{year}-{month}-{day} {str(beijing_hour).zfill(2)}:{minute}:{second}"

            elif len(utc_str) == 14:  # 格式: YYYYMMDDHHmmss
                year = utc_str[0:4]
                month = utc_str[4:6]
                day = utc_str[6:8]
                hour = int(utc_str[8:10])
                minute = utc_str[10:12]
                second = utc_str[12:14]

                # 转换为北京时间（UTC+8）
                beijing_hour = (hour + 8) % 24

                # 如果跨天了，需要调整日期
                if (hour + 8) >= 24:
                    from datetime import datetime, timedelta
                    base_date = datetime(int(year), int(month), int(day))
                    adjusted_date = base_date + timedelta(days=1)
                    year = str(adjusted_date.year)
                    month = str(adjusted_date.month).zfill(2)
                    day = str(adjusted_date.day).zfill(2)

                return f"{year}-{month}-{day} {str(beijing_hour).zfill(2)}:{minute}:{second}"
        except Exception as e:
            logging.error(f"解析时间失败: {str(e)}")
        return utc_str

    def set_device_time(self, time_cmd):
        """专门用于设置设备时间的方法"""
        try:
            logging.info(f"准备设置设备时间: {time_cmd}")

            # 验证时间指令格式
            if 'TIME=' in time_cmd:
                time_str = time_cmd.split('TIME=')[1]

                if len(time_str) == 14 and time_str.isdigit():
                    # 设置为当前配置指令
                    self.time_config = time_cmd
                    logging.info(f"时间设置指令已准备: {time_cmd}")
                    return True
                else:
                    logging.error(f"时间格式不正确: {time_str}")
                    return False
            else:
                logging.error(f"无效的时间指令格式: {time_cmd}")
                return False

        except Exception as e:
            logging.error(f"设置设备时间异常: {str(e)}")
            return False

if __name__ == "__main__":
    # 添加Web监控的日志处理器
    add_log_handler()

    # 启动TCP服务器
    server = DeviceTCPServer()

    # 正确地设置全局变量（不需要在这里使用global关键字）
    tcp_server_instance = server

    # 初始化web监控的TCP服务器引用
    init_tcp_server(server)

    # 确保初始化完成
    time.sleep(1)
    logging.info("TCP服务器初始化完成")

    # 启动TCP服务器线程
    tcp_thread = threading.Thread(target=server.start)
    tcp_thread.daemon = True  # 设置为守护线程，主线程退出时自动结束
    tcp_thread.start()

    # 启动Web服务器
    from web_monitor import app
    app.run(host='0.0.0.0', port=5000)
