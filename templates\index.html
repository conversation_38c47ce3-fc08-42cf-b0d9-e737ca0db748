<!DOCTYPE html>
<html>
<head>
    <title>设备监控系统</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2ecc71;
            --dark-color: #34495e;
            --light-color: #f5f5f5;
            --border-color: #ddd;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
            --accent-color: #e74c3c;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f8fa;
            color: #333;
            line-height: 1.4;
        }
        .container {
            max-width: 1700px;
            margin: 8px auto 0 auto;
            padding: 0 16px;
            width: 100%;
            overflow-x: hidden;
        }
        .header {
            background-color: #34495e;
            color: white;
            padding: 12px 24px 12px 24px;
            margin-bottom: 12px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.07);
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: 48px;
        }
        .header h1 {
            margin: 0;
            font-size: 26px;
            font-weight: bold;
            letter-spacing: 1px;
        }
        .panel-link-btn {
            background: #3498db;
            color: #fff;
            border: none;
            border-radius: 4px;
            padding: 10px 32px;
            font-size: 18px;
            cursor: pointer;
            margin-left: 10px;
            transition: background 0.2s;
            font-weight: bold;
            text-decoration: none;
            box-shadow: 0 1px 4px rgba(52,152,219,0.08);
        }
        .panel-link-btn:hover {
            background: #217dbb;
        }
        .panel {
            background: white;
            padding: 16px 20px 12px 20px;
            margin-bottom: 16px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.07);
            width: 100%;
            overflow: hidden;
        }
        .panel-title {
            margin-top: 0;
            margin-bottom: 6px;
            padding-bottom: 4px;
            border-bottom: 1px solid var(--border-color);
            font-size: 16px;
            color: var(--dark-color);
        }
        .control-panel {
            margin-bottom: 15px;
        }
        .input-group {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
        }

        .command-row .input-group {
            flex: none;
        }

        .command-row label {
            font-size: 14px;
            color: var(--dark-color);
            white-space: nowrap;
        }
        input[type="text"],
        input[type="number"] {
            padding: 6px 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
        }

        input[type="text"] {
            width: 280px;
        }

        #commandInput {
            width: 350px;
        }

        input[type="number"] {
            width: 70px;
        }

        select {
            padding: 6px 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
            background-color: white;
        }

        button {
            padding: 6px 12px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            white-space: nowrap;
            transition: background-color 0.2s;
        }

        button:hover {
            background-color: #2980b9;
        }

        button.secondary {
            background-color: var(--secondary-color);
        }

        button.secondary:hover {
            background-color: #27ae60;
        }

        .data-container {
            display: flex;
            gap: 15px;
        }

        .data-panel {
            flex: 1;
            height: 350px; /* 缩小数据面板高度 */
            overflow-y: auto;
            position: relative;
        }

        .log-panel {
            height: 400px; /* 扩大日志面板高度 */
            width: 100%;
        }

        .data-content {
            font-family: "Consolas", monospace;
            white-space: pre-wrap;
            font-size: 13px;
            line-height: 1.3;
            height: 100%;
            overflow-y: auto;
            word-wrap: break-word;
            word-break: break-all;
            overflow-wrap: break-word;
        }

        #deviceData {
            padding: 0;
        }

        .device-info {
            margin-bottom: 6px;
            padding: 6px;
            border-radius: 4px;
            background-color: rgba(52, 152, 219, 0.05);
            border-left: 3px solid var(--primary-color);
        }

        .device-info-header {
            color: var(--dark-color);
            font-weight: bold;
            margin-bottom: 2px;
            font-size: 13px;
        }

        .device-info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: 2px 10px;
        }

        .device-info-item {
            display: flex;
            margin-bottom: 1px;
        }

        .device-info-label {
            width: 75px;
            font-weight: bold;
            color: var(--dark-color);
        }

        .device-info-value {
            flex: 1;
        }

        #rawData {
            font-size: 12px;
            color: #555;
        }

        .raw-data-item {
            padding: 5px;
            margin-bottom: 3px;
            border-bottom: 1px solid #eee;
        }

        .raw-data-time {
            color: var(--dark-color);
            font-weight: bold;
            font-size: 12px;
        }

        .raw-data-content {
            margin-top: 2px;
            word-break: break-all;
        }

        #logData {
            font-size: 12px;
            line-height: 1.2; /* 更紧凑的行高 */
        }

        .log-item {
            margin-bottom: 1px; /* 更紧凑的间距 */
            padding: 1px 2px; /* 更小的内边距 */
        }

        .log-item.info {
            background-color: rgba(52, 152, 219, 0.1);
        }

        .log-item.success {
            background-color: rgba(46, 204, 113, 0.1);
        }

        .log-item.error {
            background-color: rgba(231, 76, 60, 0.1);
        }

        .log-time {
            color: var(--dark-color);
            font-weight: bold;
        }

        .log-level {
            font-weight: bold;
            margin-left: 5px;
        }

        .log-message {
            margin-left: 5px;
        }

        .spacer {
            flex: 1;
        }

        @media (max-width: 768px) {
            .data-container {
                flex-direction: column;
            }

            .data-panel {
                height: 450px;
                max-height: 470px;
            }

            .log-panel {
                height: 450px;
            }

            .data-panels-row {
                flex-direction: column !important;
                gap: 12px !important;
            }

            .container {
                padding: 0 12px;
                margin: 6px auto 0 auto;
            }

            .panel {
                padding: 8px 12px 6px 12px;
                margin-bottom: 12px;
            }

            .panel-title {
                margin-bottom: 3px !important;
                padding-bottom: 2px !important;
            }

            .top-controls {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
            }

            .command-row {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
            }

            .input-group {
                margin-right: 0;
            }

            .command-row .input-group {
                justify-content: space-between;
            }

            input[type="text"] {
                width: 100%;
            }

            #commandInput {
                width: 100%;
            }

            .device-switch, .system-time {
                justify-content: center;
            }
        }

        .system-time {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 18px;
            background-color: rgba(52, 152, 219, 0.08);
            border-radius: 6px;
            font-size: 16px;
        }

        .current-time {
            font-weight: bold;
            font-size: 18px;
            color: #34495e;
        }

        .time-btn {
            background-color: #e74c3c;
            color: #fff;
            border: none;
            border-radius: 4px;
            padding: 6px 22px;
            font-size: 16px;
            margin-left: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .time-btn:hover {
            background-color: #c0392b;
        }

        /* 顶部控制区域 */
        .top-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 18px;
            gap: 30px;
        }

        /* 设备连接开关样式 */
        .device-switch {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 18px;
            background-color: rgba(52, 152, 219, 0.08);
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            font-size: 16px;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 48px;
            height: 28px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0; left: 0; right: 0; bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 28px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 22px;
            width: 22px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #2ecc71;
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }

        .switch-label {
            font-weight: bold;
            color: #34495e;
            font-size: 16px;
        }

        .switch-status {
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
        }

        .switch-status.enabled {
            background-color: rgba(46, 204, 113, 0.18);
            color: #2ecc71;
        }

        .switch-status.disabled {
            background-color: rgba(231, 76, 60, 0.18);
            color: #e74c3c;
        }

        /* 配置选项行 */
        .config-row {
            display: flex;
            align-items: center;
            gap: 32px;
            margin-bottom: 12px;
            flex-wrap: wrap;
            justify-content: space-between;
        }
        .input-group > * {
            height: 38px;
            font-size: 16px;
        }
        .input-group select,
        .input-group input[type="number"],
        .input-group button {
            min-width: 90px;
        }
        .input-group label {
            min-width: 90px;
        }
        .input-group span {
            min-width: 60px;
            display: inline-block;
        }
        .input-group button {
            margin-left: 8px;
        }
        .input-group {
            gap: 12px;
        }
        .config-row {
            margin-bottom: 12px;
        }
        .stat-block {
            background: #f8f8f8;
            border-radius: 8px;
            padding: 10px 22px;
            margin-right: 16px;
            display: inline-block;
            min-width: 120px;
            text-align: center;
        }
        .stat-label {
            color: #888;
            margin-right: 2px;
        }
        .stat-value {
            font-weight: bold;
            font-size: 20px;
        }
        .stat-s { color: #3498db; }
        .stat-b { color: #2ecc71; }
        .stat-total { color: #e74c3c; }
        .stat-repeat { color: #f39c12; }
        .panel-link {
            float: right;
            margin-top: -8px;
            margin-right: 10px;
            font-size: 16px;
            color: #3498db;
            text-decoration: underline;
            cursor: pointer;
        }
        .secondary {
            background: #2ecc71;
            color: #fff;
            border: none;
            border-radius: 4px;
            padding: 0 18px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.2s;
        }
        .secondary:hover {
            background: #27ae60;
        }
        button {
            background: #3498db;
            color: #fff;
            border: none;
            border-radius: 4px;
            padding: 0 18px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.2s;
        }
        button:hover {
            background: #217dbb;
        }
        .log-panel {
            width: 100%;
            word-break: break-all;
            white-space: pre-wrap;
        }
        .data-panels-row {
            display: flex;
            gap: 32px;
            width: 100%;
            margin-bottom: 24px;
            background: none;
            box-shadow: none;
            padding: 0;
        }
        .data-panel {
            flex: 1;
            min-width: 0;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.07);
            padding: 18px 18px 10px 18px;
            display: flex;
            flex-direction: column;
        }
        .data-content {
            height: 600px;
            overflow-y: auto;
            font-family: Consolas, monospace;
            font-size: 15px;
            background: #fff;
            border-radius: 8px;
            padding: 10px;
            word-break: break-all;
            white-space: pre-wrap;
            overflow-wrap: break-word;
            max-width: 100%;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div style="display: flex; align-items: center;">
                <h1 style="margin-right: 18px;">设备监控系统</h1>
                <!-- 设备连接开关紧跟标题，去掉文字和状态，仅保留开关 -->
                <label class="switch">
                    <input type="checkbox" id="deviceConnectionSwitch" checked>
                    <span class="slider"></span>
                </label>
            </div>
            <div style="display: flex; gap: 12px;">
                <a class="panel-link-btn" href="/track_view">定位展示</a>
                <a class="panel-link-btn" href="/data_view">数据展示</a>
            </div>
        </div>

        <div class="panel control-panel">
            <!-- 统计区和系统时间一行，两端对齐 -->
            <div class="stat-row" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                <div class="stat-group" style="display: flex; align-items: center; gap: 16px;">
                    <span class="stat-block"><span class="stat-label">S数据:</span> <span id="sDataCount" class="stat-value stat-s">0</span></span>
                    <span class="stat-block"><span class="stat-label">B数据:</span> <span id="bDataCount" class="stat-value stat-b">0</span></span>
                    <span class="stat-block"><span class="stat-label">总计:</span> <span id="totalDataCount" class="stat-value stat-total">0</span></span>
                    <button class="secondary" onclick="resetDataCounts()">清零计数</button>
                    <button class="secondary" onclick="resetAllData()">数据清零</button>
                </div>
                <!-- 重复数据统计靠右对齐 -->
                <div style="display: flex; align-items: center; gap: 12px;">
                    <span class="stat-block"><span class="stat-label">重复数据:</span> <span id="repeatDataCount" class="stat-value stat-repeat">0</span></span>
                    <button class="secondary" onclick="resetRepeatDataCount()">清零重复</button>
                </div>
            </div>

            <!-- 指令下发区：一行平铺布局 -->
            <div class="command-row" style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap;">
                <!-- 休眠 -->
                <div class="input-group" style="display: flex; align-items: center; gap: 4px;">
                    <label>休眠</label>
                    <input type="number" id="sleepValue" placeholder="" style="width: 60px;">
                    <select id="sleepUnit" style="width: 50px;">
                        <option value="S">秒</option>
                        <option value="M">分</option>
                        <option value="H">时</option>
                        <option value="D">天</option>
                    </select>
                </div>

                <!-- 工段 -->
                <div class="input-group" style="display: flex; align-items: center; gap: 4px;">
                    <label>工段</label>
                    <select id="workStartHour" style="width: 60px;">
                        <option value="">开始</option>
                    </select>
                    <select id="workEndHour" style="width: 60px;">
                        <option value="">结束</option>
                    </select>
                </div>

                <!-- 打包 -->
                <div class="input-group" style="display: flex; align-items: center; gap: 4px;">
                    <label>打包</label>
                    <input type="number" id="packValue" placeholder="" style="width: 60px;">
                </div>

                <!-- 速度 -->
                <div class="input-group" style="display: flex; align-items: center; gap: 4px;">
                    <label>速度</label>
                    <input type="number" id="speedValue" placeholder="" style="width: 60px;">
                </div>

                <!-- 速休 -->
                <div class="input-group" style="display: flex; align-items: center; gap: 4px;">
                    <label>速休</label>
                    <input type="number" id="fastSleepValue" placeholder="" style="width: 60px;">
                </div>

                <!-- 设置按钮 -->
                <button class="primary" onclick="sendCombinedCommand()" style="margin-left: 8px;">设置</button>
            </div>
        </div>

        <div class="panel data-panels-row" style="display: flex; gap: 20px; width: 100%; margin-bottom: 16px; background: none; box-shadow: none; padding: 0;">
            <div class="panel data-panel" style="flex:1; min-width: 0; max-height: 560px; padding-top: 12px;">
                <h2 class="panel-title" style="margin-top: 0; margin-bottom: 4px; padding-bottom: 3px;">设备数据</h2>
                <div class="data-content" id="deviceData" style="height: 520px; overflow-y: auto; font-family: Consolas, monospace; font-size: 15px; background: #fff; border-radius: 8px; padding: 8px;"></div>
            </div>
            <div class="panel data-panel" style="flex:1; min-width: 0; max-height: 560px; padding-top: 12px;">
                <h2 class="panel-title" style="margin-top: 0; margin-bottom: 4px; padding-bottom: 3px;">原始数据</h2>
                <div class="data-content" id="rawData" style="height: 520px; overflow-y: auto; font-family: Consolas, monospace; font-size: 14px; background: #fff; border-radius: 8px; padding: 8px;"></div>
            </div>
        </div>

        <div class="panel log-panel" style="padding-top: 12px;">
            <h2 class="panel-title" style="margin-top: 0; margin-bottom: 4px; padding-bottom: 3px;">系统日志</h2>
            <!-- 调整系统日志卡片高度，减少留白，增加显示内容 -->
            <div class="data-content" id="logData" style="height: 480px; overflow-y: auto; max-height: 480px; word-wrap: break-word; word-break: break-all; padding: 8px;">
                <!-- 日志数据会在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let logEventSource = null;
        let logBuffer = []; // 日志缓冲区
        let logUpdateTimer = null;

        // 设备连接开关相关功能
        document.getElementById('deviceConnectionSwitch').addEventListener('change', function() {
            const enabled = this.checked;
            fetch('/api/device_connection_switch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ enabled: enabled })
            })
            .then(response => response.json())
            .then(result => {
                if (result.status === 'success') {
                    updateSwitchStatus(result.enabled);
                } else {
                    alert('设置失败: ' + result.message);
                    // 恢复开关状态
                    this.checked = !enabled;
                }
            })
            .catch(error => {
                alert('设置失败: ' + error);
                // 恢复开关状态
                this.checked = !enabled;
            });
        });

        function updateSwitchStatus(enabled) {
            // 只切换checkbox本身，不操作不存在的switchStatus元素
            const switchElement = document.getElementById('deviceConnectionSwitch');
            // 设置checkbox状态
            switchElement.checked = !!enabled;
            // 可根据需要在此处添加其它UI提示
        }

        // 初始化时获取开关状态
        function initDeviceSwitch() {
            fetch('/api/device_connection_switch')
            .then(response => response.json())
            .then(result => {
                if (result.status === 'success') {
                    updateSwitchStatus(result.enabled);
                }
            })
            .catch(error => {
                console.error('获取设备开关状态失败:', error);
            });
        }

        // 初始化实时日志
        function initRealTimeLogs() {
            // 首先加载初始日志
            fetch('/api/logs')
            .then(response => response.json())
            .then(logs => {
                displayLogs(logs);
            })
            .catch(error => {
                console.error('加载初始日志失败:', error);
            });

            // 建立SSE连接用于实时日志
            if (logEventSource) {
                logEventSource.close();
            }

            logEventSource = new EventSource('/api/logs/stream');

            logEventSource.onmessage = function(event) {
                try {
                    const logData = JSON.parse(event.data);
                    if (logData.type === 'heartbeat') {
                        return; // 跳过心跳消息
                    }

                    // 添加到缓冲区
                    logBuffer.unshift(logData);

                    // 限制缓冲区大小
                    if (logBuffer.length > 1000) {
                        logBuffer = logBuffer.slice(0, 1000);
                    }

                    // 延迟更新显示（避免频繁刷新）
                    if (logUpdateTimer) {
                        clearTimeout(logUpdateTimer);
                    }
                    logUpdateTimer = setTimeout(() => {
                        displayLogs(logBuffer);
                    }, 100); // 100ms延迟批量更新

                } catch (error) {
                    console.error('解析日志数据失败:', error);
                }
            };

            logEventSource.onerror = function(event) {
                console.error('实时日志连接出错:', event);
                // 5秒后尝试重连
                setTimeout(() => {
                    if (!logEventSource || logEventSource.readyState === EventSource.CLOSED) {
                        console.log('尝试重连实时日志...');
                        initRealTimeLogs();
                    }
                }, 5000);
            };

            logEventSource.onopen = function(event) {
                console.log('实时日志连接已建立');
            };
        }

        // 显示日志函数
        function displayLogs(logs) {
            const logDataDiv = document.getElementById('logData');
            if (logs && logs.length > 0) {
                logDataDiv.innerHTML = logs.map(log =>
                    `<div class="log-item">
                        <span class="log-time">[${log.time}]</span>
                        <span class="log-level">${log.level}:</span>
                        <span class="log-message">${log.message}</span>
                    </div>`
                ).join('');

                // 自动滚动到顶部显示最新日志
                logDataDiv.scrollTop = 0;
            } else {
                logDataDiv.innerHTML = '<div style="text-align: center; color: #666; margin-top: 20px;">暂无日志数据</div>';
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化工段时间选择器
            const workStartSelect = document.getElementById('workStartHour');
            const workEndSelect = document.getElementById('workEndHour');

            for (let i = 0; i <= 23; i++) {
                const val = i.toString().padStart(2, '0');
                const startOption = new Option(i + '点', val);
                const endOption = new Option(i + '点', val);
                workStartSelect.appendChild(startOption);
                workEndSelect.appendChild(endOption);
            }

            initDeviceSwitch();
            initRealTimeLogs(); // 初始化实时日志
            // 初始化数据计数器
            updateDataCounts();
        });

        function formatDeviceData(data) {
            const timeMatch = data.match(/\[(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\]/);
            const timestamp = timeMatch ? timeMatch[1] : '';

            const formattedData = data.replace(/\[.*?\]\n([\s\S]*)/m, (match, content) => {
                const lines = content.trim().split('\n');
                const infoItems = [];

                lines.forEach(line => {
                    const parts = line.split(':').map(p => p.trim());
                    if (parts.length >= 2) {
                        const label = parts[0];
                        const value = parts.slice(1).join(':');
                        infoItems.push(`<div class="device-info-item">
                            <span class="device-info-label">${label}</span>
                            <span class="device-info-value">${value}</span>
                        </div>`);
                    } else {
                        infoItems.push(`<div class="device-info-item">${line}</div>`);
                    }
                });

                return `<div class="device-info">
                    <div class="device-info-header">[${timestamp}]</div>
                    <div class="device-info-grid">
                        ${infoItems.join('')}
                    </div>
                </div>`;
            });

            return formattedData;
        }

        // 修改updateData函数，只更新设备数据和原始数据
        function updateData() {
            Promise.all([
                fetch('/api/data').then(response => response.json()),
                fetch('/api/raw_data').then(response => response.json())
            ])
            .then(([deviceData, rawData]) => {
                // 更新设备数据 - 只显示最新的1组数据
                const deviceDataDiv = document.getElementById('deviceData');
                if (deviceData.length > 0) {
                    const latestData = deviceData.slice(0, 1); // 只取最新的1组数据
                    const formattedData = latestData.map(item => formatDeviceData(
                        `[${item.time}]\n${item.data}`
                    )).join('');
                    deviceDataDiv.innerHTML = formattedData;
                } else {
                    deviceDataDiv.innerHTML = '<div style="text-align: center; color: #666; margin-top: 20px;">暂无设备数据</div>';
                }

                // 更新原始数据
                const rawDataDiv = document.getElementById('rawData');
                rawDataDiv.innerHTML = rawData.map(item =>
                    `<div class="raw-data-item">
                        <div class="raw-data-time">[${item.time}]</div>
                        <div class="raw-data-content">${item.data}</div>
                    </div>`
                ).join('');
            })
            .catch(error => {
                console.error('更新数据失败:', error);
                // 在页面上显示错误提示
                const deviceDataDiv = document.getElementById('deviceData');
                const rawDataDiv = document.getElementById('rawData');
                deviceDataDiv.innerHTML = '<div style="text-align: center; color: red; margin-top: 20px;">数据加载失败</div>';
                rawDataDiv.innerHTML = '<div style="text-align: center; color: red; margin-top: 20px;">原始数据加载失败</div>';
            });
        }

        function sendCommand() {
            const command = document.getElementById('commandInput').value;
            if (!command) {
                alert('请输入指令');
                return;
            }

            fetch('/api/send_command', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ command: command })
            })
            .then(response => response.json())
            .then(result => {
                alert(result.message);
                if (result.status === 'success') {
                    document.getElementById('commandInput').value = '';
                }
            })
            .catch(error => {
                alert('发送指令失败: ' + error);
            });
        }

        // 发送组合指令
        function sendCombinedCommand() {
            let commandParts = ['ZL'];

            // 休眠设置
            const sleepValue = document.getElementById('sleepValue').value;
            const sleepUnit = document.getElementById('sleepUnit').value;
            if (sleepValue && sleepValue.trim() !== '') {
                commandParts.push(`${sleepUnit}${sleepValue}`);
            }

            // 工段设置
            const workStartHour = document.getElementById('workStartHour').value;
            const workEndHour = document.getElementById('workEndHour').value;
            if (workStartHour && workEndHour && workStartHour !== '' && workEndHour !== '') {
                commandParts.push(`F${workStartHour}E${workEndHour}`);
            }

            // 打包设置
            const packValue = document.getElementById('packValue').value;
            if (packValue && packValue.trim() !== '') {
                commandParts.push(`N${packValue}`);
            }

            // 速度设置
            const speedValue = document.getElementById('speedValue').value;
            if (speedValue && speedValue.trim() !== '') {
                commandParts.push(`A${speedValue}`);
            }

            // 速休设置
            const fastSleepValue = document.getElementById('fastSleepValue').value;
            if (fastSleepValue && fastSleepValue.trim() !== '') {
                commandParts.push(`B${fastSleepValue}`);
            }

            // 如果所有参数都为空，使用默认指令
            let finalCommand;
            if (commandParts.length === 1) {
                finalCommand = 'ZL+S30';
            } else {
                finalCommand = commandParts.join('+');
            }

            console.log(`准备发送组合指令: ${finalCommand}`);

            fetch('/api/send_command', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ command: finalCommand })
            })
            .then(response => response.json())
            .then(result => {
                if (result.status === 'success') {
                    console.log(`指令发送成功: ${finalCommand}`);
                } else {
                    console.error(`指令发送失败: ${result.message}`);
                }
            })
            .catch(error => {
                console.error('发送指令失败:', error);
            });
        }

        // 页面关闭时清理SSE连接
        window.addEventListener('beforeunload', function() {
            if (logEventSource) {
                logEventSource.close();
            }
        });

        // 只更新设备数据和原始数据，日志已经实时更新
        setInterval(updateData, 5000);
        updateData();

        // 定期更新数据计数器
        setInterval(updateDataCounts, 3000);

        // 更新数据计数器显示
        function updateDataCounts() {
            fetch('/api/data_counts')
            .then(response => response.json())
            .then(result => {
                if (result.status === 'success') {
                    document.getElementById('sDataCount').textContent = result.data.s_count;
                    document.getElementById('bDataCount').textContent = result.data.b_count;
                    document.getElementById('totalDataCount').textContent = result.data.total_count;
                    document.getElementById('repeatDataCount').textContent = result.data.repeat_count;
                }
            })
            .catch(error => {
                console.error('获取数据计数失败:', error);
            });
        }

        // 清零数据计数器
        function resetDataCounts() {
            fetch('/api/reset_data_counts', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => response.json())
            .then(result => {
                if (result.status === 'success') {
                    updateDataCounts(); // 更新显示
                }
            })
            .catch(error => {
                console.error('清零失败:', error);
            });
        }

        // 清零重复数据计数器
        function resetRepeatDataCount() {
            fetch('/api/reset_repeat_data_count', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => response.json())
            .then(result => {
                if (result.status === 'success') {
                    updateDataCounts(); // 更新显示
                }
            })
            .catch(error => {
                console.error('清零重复数据失败:', error);
            });
        }

        // 清零所有数据
        function resetAllData() {
            if (confirm('确定要清零所有数据吗？这将删除所有设备数据和原始数据。')) {
                fetch('/api/reset_all_data', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.status === 'success') {
                        alert('所有数据已清零！');
                        updateDataCounts(); // 更新显示
                        updateData(); // 刷新数据面板
                    } else {
                        alert('清零失败: ' + result.message);
                    }
                })
                .catch(error => {
                    console.error('清零所有数据失败:', error);
                });
            }
        }
    </script>
</body>
</html>
