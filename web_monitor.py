from flask import Flask, render_template, jsonify, request, Response
import threading
import logging
import datetime
import json
import time

app = Flask(__name__)

# 禁用Flask默认的访问日志
app.logger.disabled = True
logging.getLogger('werkzeug').disabled = True

# 用于存储最新的设备数据
device_data = []
# 用于存储原始数据
raw_data = []
# 最大保存记录数
MAX_RECORDS = 100
# 用于存储最近的日志以供显示
recent_logs = []
# 用于存储当前连接的设备
active_clients = {}
# TCP服务器引用
tcp_server = None
# 用于实时日志推送的客户端列表
log_clients = []

def add_log_handler():
    class QueueHandler(logging.Handler):
        def emit(self, record):
            # 扩展日志关键词，捕获更多操作
            keywords = [
                '收到数据', '新的连接', '发送配置', 'HB数据包含记录数',
                '设备连接开关', '客户端连接关闭', '向设备', '发送指令',
                '解析数据', '处理客户端', '收到AT命令', '收到TX调试信息',
                '时间设置指令', '间隔配置', '工作时间段配置', '拒绝连接',
                '配置已保存', '指令已发送', 'TCP服务器启动', '监听地址',
                'ACCEPT', 'OK', 'CONNECTION_DENIED', '数据长度', '解析',
                '启动', '初始化', '连接', '断开', '发送', '接收'
            ]

            message = record.getMessage()

            # 检查是否包含关键词
            if any(keyword in message for keyword in keywords):
                # 跳过一些过于冗长的解析数据日志
                if '解析数据:\n' in message:
                    return

                log_entry = {
                    'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],  # 包含毫秒
                    'level': record.levelname,
                    'message': message
                }

                # 添加到recent_logs
                recent_logs.insert(0, log_entry)

                # 保持recent_logs大小
                if len(recent_logs) > 1000:  # 增加日志保存数量
                    recent_logs.pop()

                # 实时推送给所有连接的客户端
                push_log_to_clients(log_entry)

    # 添加队列处理器到根日志记录器
    logging.getLogger().addHandler(QueueHandler())

def push_log_to_clients(log_entry):
    """推送日志到所有连接的SSE客户端"""
    if log_clients:
        data = json.dumps(log_entry)
        # 复制列表以避免迭代时修改
        clients_copy = log_clients.copy()
        for client in clients_copy:
            try:
                client.put(f"data: {data}\n\n")
            except:
                # 如果客户端断开连接，从列表中移除
                if client in log_clients:
                    log_clients.remove(client)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/data_view')
def data_view():
    return render_template('data_view.html')

@app.route('/track_view')
def track_view():
    return render_template('track_view.html')

@app.route('/api/data')
def get_data():
    return jsonify(device_data)

@app.route('/api/logs')
def get_logs():
    # 返回最近的日志（用于初始加载）
    return jsonify(recent_logs[:100])  # 只返回最近100条

@app.route('/api/logs/stream')
def log_stream():
    """Server-Sent Events 端点，用于实时日志推送"""
    import queue

    def event_stream():
        # 为这个客户端创建一个队列
        client_queue = queue.Queue()
        log_clients.append(client_queue)

        try:
            while True:
                try:
                    # 等待新的日志消息
                    data = client_queue.get(timeout=30)  # 30秒超时
                    yield data
                except queue.Empty:
                    # 发送心跳保持连接
                    yield "data: {\"type\": \"heartbeat\"}\n\n"
        except GeneratorExit:
            # 客户端断开连接
            if client_queue in log_clients:
                log_clients.remove(client_queue)

    return Response(event_stream(), mimetype='text/event-stream',
                   headers={
                       'Cache-Control': 'no-cache',
                       'Connection': 'keep-alive',
                       'Access-Control-Allow-Origin': '*'
                   })

@app.route('/api/raw_data')
def get_raw_data():
    return jsonify(raw_data)

@app.route('/api/device_connection_switch', methods=['GET', 'POST'])
def device_connection_switch():
    """设备连接开关API"""
    if tcp_server is None:
        return jsonify({'status': 'error', 'message': 'TCP服务器未初始化'})

    if request.method == 'POST':
        # 设置开关状态
        enabled = request.json.get('enabled', True)
        tcp_server.set_device_connection_enabled(enabled)
        return jsonify({'status': 'success', 'enabled': enabled})
    else:
        # 获取开关状态
        enabled = tcp_server.get_device_connection_enabled()
        return jsonify({'status': 'success', 'enabled': enabled})

# 移除配置发送开关API

@app.route('/api/data_counts', methods=['GET'])
def get_data_counts():
    """获取数据计数API"""
    if tcp_server is None:
        return jsonify({'status': 'error', 'message': 'TCP服务器未初始化'})

    counts = tcp_server.get_data_counts()
    repeat_count = tcp_server.get_repeat_data_count() if tcp_server else 0
    counts['repeat_count'] = repeat_count
    return jsonify({'status': 'success', 'data': counts})

@app.route('/api/reset_data_counts', methods=['POST'])
def reset_data_counts():
    """清零数据计数API"""
    if tcp_server is None:
        return jsonify({'status': 'error', 'message': 'TCP服务器未初始化'})

    result = tcp_server.reset_data_counts()
    if result:
        return jsonify({'status': 'success', 'message': '数据计数器已清零'})
    else:
        return jsonify({'status': 'error', 'message': '清零失败'})

@app.route('/api/send_command', methods=['POST'])
def send_command():
    cmd = request.json.get('command')
    if not cmd:
        return jsonify({'status': 'error', 'message': '命令不能为空'})

    try:
        logging.info(f"收到配置请求: {cmd}")

        if tcp_server is None:
            logging.error("TCP服务器未初始化")
            return jsonify({'status': 'error', 'message': 'TCP服务器未初始化，请检查服务器状态'})

        # 去掉指令格式限制，允许自由输入
        # 检查是否是标准配置指令格式
        if cmd.startswith('ZL+') or cmd.startswith('M') or cmd.startswith('S') or 'TIME=' in cmd or ('F' in cmd and 'E' in cmd):
            # 使用原有的配置指令处理逻辑
            result = tcp_server.set_config_command(cmd)
            if result:
                logging.info(f"配置已保存: {cmd}，将在设备下次连接时发送")
                return jsonify({'status': 'success', 'message': '配置已保存，将在设备下次连接时发送'})
            else:
                logging.error(f"配置指令格式错误: {cmd}")
                return jsonify({'status': 'error', 'message': '配置格式不正确'})
        else:
            # 对于非标准格式的指令，直接发送到所有连接的设备
            if not active_clients:
                return jsonify({'status': 'error', 'message': '当前没有连接的设备'})

            sent_count = 0
            for address, client_socket in active_clients.items():
                try:
                    # 确保指令以\r\n结尾
                    command_to_send = cmd if cmd.endswith('\r\n') else cmd + '\r\n'
                    client_socket.send(command_to_send.encode())
                    logging.info(f"向设备 {address} 发送自定义指令: {cmd}")
                    sent_count += 1
                except Exception as e:
                    logging.error(f"向设备 {address} 发送指令失败: {str(e)}")

            if sent_count > 0:
                return jsonify({'status': 'success', 'message': f'指令已发送到 {sent_count} 个设备'})
            else:
                return jsonify({'status': 'error', 'message': '指令发送失败'})

    except Exception as e:
        logging.error(f"处理配置请求异常: {str(e)}")
        return jsonify({'status': 'error', 'message': f'配置更新失败: {str(e)}'})

@app.route('/api/repeat_data_count', methods=['GET'])
def get_repeat_data_count():
    if tcp_server is None:
        return jsonify({'status': 'error', 'message': 'TCP服务器未初始化'})
    return jsonify({'status': 'success', 'count': tcp_server.get_repeat_data_count()})

@app.route('/api/reset_repeat_data_count', methods=['POST'])
def reset_repeat_data_count():
    if tcp_server is None:
        return jsonify({'status': 'error', 'message': 'TCP服务器未初始化'})
    tcp_server.reset_repeat_data_count()
    return jsonify({'status': 'success', 'message': '重复数据计数器已清零'})

@app.route('/api/reset_all_data', methods=['POST'])
def reset_all_data():
    global device_data, raw_data
    device_data.clear()
    raw_data.clear()
    if tcp_server:
        tcp_server.reset_data_counts()
        tcp_server.reset_repeat_data_count()
    return jsonify({'status': 'success', 'message': '所有数据已清零'})

@app.route('/api/track_data')
def get_track_data():
    """获取指定CCID的轨迹数据"""
    ccid = request.args.get('ccid', '').strip()
    if not ccid:
        return jsonify({'status': 'error', 'message': '请提供CCID号'})

    track_points = []

    # 遍历原始数据，提取包含指定CCID的GPS数据
    for item in raw_data:
        data_str = item['data']

        # 检查是否包含指定的CCID
        if ccid in data_str:
            # 解析GPS数据
            gps_data = parse_gps_data(data_str, item['time'])
            if gps_data:
                track_points.append(gps_data)

    # 按时间排序（最早的在前面）
    track_points.sort(key=lambda x: x['time'])

    return jsonify({
        'status': 'success',
        'data': track_points,
        'count': len(track_points)
    })

@app.route('/api/latest_location')
def get_latest_location():
    """获取最新的位置数据"""
    if not raw_data:
        return jsonify({'status': 'error', 'message': '暂无数据'})

    # 从最新的原始数据中查找GPS数据
    for item in raw_data:
        gps_data = parse_gps_data(item['data'], item['time'])
        if gps_data:
            # 添加GPS状态描述
            gps_data['gps_status'] = get_gps_status_from_data(item['data'])
            # 添加ICCID
            gps_data['iccid'] = extract_iccid_from_data(item['data'])
            return jsonify({'status': 'success', 'data': gps_data})

    return jsonify({'status': 'error', 'message': '未找到有效的GPS数据'})

def parse_gps_data(data_str, receive_time):
    """解析GPS数据，提取经纬度和时间信息"""
    try:
        # 处理HX+和HB+格式的数据
        if data_str.startswith('HX+') or data_str.startswith('HB+'):
            parts = data_str.replace('HX+', '').replace('HB+', '').split('+')
            if len(parts) >= 16:
                longitude_raw = parts[0]  # 原始经度，如"11957.64581"
                latitude_raw = parts[1]   # 原始纬度，如"3016.51036"
                utc_time = parts[2]       # UTC时间，如"065026.220524"

                # 转换经纬度格式（从度分格式转为十进制度）
                longitude = convert_coordinate(longitude_raw)
                latitude = convert_coordinate(latitude_raw)

                # 解析时间
                parsed_time = parse_utc_time_simple(utc_time)

                if longitude and latitude:
                    return {
                        'longitude': longitude,
                        'latitude': latitude,
                        'time': parsed_time or receive_time,
                        'raw_data': data_str
                    }

        # 处理HY格式的数据
        elif data_str.startswith('HY'):
            first_plus = data_str.find('+')
            if first_plus != -1:
                data_content = data_str[first_plus+1:]
                if data_content.endswith('E'):
                    data_content = data_content[:-1]

                parts = data_content.split('+')
                if len(parts) >= 17:
                    longitude_raw = parts[0]
                    latitude_raw = parts[1]
                    utc_time = parts[2]

                    longitude = convert_coordinate(longitude_raw)
                    latitude = convert_coordinate(latitude_raw)
                    parsed_time = parse_utc_time_simple(utc_time)

                    if longitude and latitude:
                        return {
                            'longitude': longitude,
                            'latitude': latitude,
                            'time': parsed_time or receive_time,
                            'raw_data': data_str
                        }
    except Exception as e:
        logging.error(f"解析GPS数据失败: {str(e)}")

    return None

def convert_coordinate(coord_str):
    """将度分格式的坐标转换为十进制度格式"""
    try:
        coord_float = float(coord_str)

        # 度分格式转换：前面是度，后面是分
        # 例如：11957.64581 = 119度57.64581分 = 119 + 57.64581/60 = 119.960764度
        # 例如：3016.51036 = 30度16.51036分 = 30 + 16.51036/60 = 30.275173度
        if coord_float > 180:  # 明显是度分格式
            degrees = int(coord_float / 100)
            minutes = coord_float - (degrees * 100)
            decimal_degrees = degrees + (minutes / 60)
            return round(decimal_degrees, 6)
        else:
            # 可能已经是十进制度格式
            return round(coord_float, 6)
    except:
        return None

def parse_utc_time_simple(utc_str):
    """简单解析UTC时间并转换为北京时间"""
    try:
        if '.' in utc_str and len(utc_str) >= 12:
            # 格式: HHmmss.DDMMYY
            time_part = utc_str.split('.')[0]
            date_part = utc_str.split('.')[1]

            hour = int(time_part[0:2])
            minute = time_part[2:4]
            second = time_part[4:6]

            day = date_part[0:2]
            month = date_part[2:4]
            year = date_part[4:6]

            # 转换为北京时间（UTC+8）
            beijing_hour = (hour + 8) % 24

            # 处理跨天
            day_adjust = 1 if (hour + 8) >= 24 else 0
            if day_adjust:
                from datetime import datetime, timedelta
                base_date = datetime(2000 + int(year), int(month), int(day))
                adjusted_date = base_date + timedelta(days=day_adjust)
                year = str(adjusted_date.year - 2000).zfill(2)
                month = str(adjusted_date.month).zfill(2)
                day = str(adjusted_date.day).zfill(2)

            return f"20{year}-{month}-{day} {str(beijing_hour).zfill(2)}:{minute}:{second}"
    except:
        pass
    return None

def get_gps_status_from_data(data_str):
    """从数据中提取GPS状态描述"""
    try:
        if data_str.startswith('HX+') or data_str.startswith('HB+'):
            parts = data_str.replace('HX+', '').replace('HB+', '').split('+')
            if len(parts) >= 5:
                status = parts[4]
                status_dict = {
                    '0': '未定位',
                    '1': '高精度定位',
                    '2': '2D定位',
                    '3': '3D定位',
                    '6': '估算值定位'
                }
                return status_dict.get(status, f'状态{status}')
        elif data_str.startswith('HY'):
            first_plus = data_str.find('+')
            if first_plus != -1:
                data_content = data_str[first_plus+1:]
                if data_content.endswith('E'):
                    data_content = data_content[:-1]
                parts = data_content.split('+')
                if len(parts) >= 5:
                    status = parts[4]
                    status_dict = {
                        '0': '未定位',
                        '1': '高精度定位',
                        '2': '2D定位',
                        '3': '3D定位',
                        '6': '估算值定位'
                    }
                    return status_dict.get(status, f'状态{status}')
    except:
        pass
    return '未知'

def extract_iccid_from_data(data_str):
    """从数据中提取ICCID"""
    try:
        # 查找19-20位数字的ICCID
        import re
        iccid_match = re.search(r'(\d{19,20})', data_str)
        if iccid_match:
            return iccid_match.group(1)
    except:
        pass
    return '未知'

def add_device_data(data):
    device_data.insert(0, {
        'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'data': data
    })
    # 保持最大记录数
    if len(device_data) > MAX_RECORDS:
        device_data.pop()

def add_raw_data(data):
    raw_data.insert(0, {
        'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'data': data
    })
    # 保持最大记录数
    if len(raw_data) > MAX_RECORDS:
        raw_data.pop()

def init_tcp_server(server):
    global tcp_server
    tcp_server = server
    logging.info("TCP服务器引用初始化成功")

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
