# 设备监控服务器部署说明

## 环境要求
- Linux服务器 (Ubuntu/CentOS/Debian)
- Python 3.6+
- systemd (用于服务管理)

## 部署步骤

### 1. 准备工作目录
```bash
# 创建工作目录
mkdir -p /root/device_monitor
cd /root/device_monitor

# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 安装依赖包
pip install flask
```

### 2. 上传项目文件
将以下文件上传到 `/root/device_monitor/` 目录：
- `device_tcp_server.py`
- `web_monitor.py`
- `templates/index.html`
- `start_server.sh`
- `device-monitor.service`

### 3. 设置脚本权限
```bash
# 给启动脚本添加执行权限
chmod +x /root/device_monitor/start_server.sh

# 创建日志目录
mkdir -p /var/log
touch /var/log/device_monitor.log
```

### 4. 手动测试运行
```bash
# 测试脚本是否正常运行
cd /root/device_monitor
./start_server.sh

# 如果正常启动，按 Ctrl+C 停止，然后配置为系统服务
```

### 5. 配置systemd服务
```bash
# 复制服务文件到systemd目录
cp device-monitor.service /etc/systemd/system/

# 重新加载systemd配置
systemctl daemon-reload

# 启用服务（开机自启）
systemctl enable device-monitor.service

# 启动服务
systemctl start device-monitor.service
```

## 服务管理命令

### 查看服务状态
```bash
systemctl status device-monitor.service
```

### 启动服务
```bash
systemctl start device-monitor.service
```

### 停止服务
```bash
systemctl stop device-monitor.service
```

### 重启服务
```bash
systemctl restart device-monitor.service
```

### 查看服务日志
```bash
# 查看实时日志
journalctl -u device-monitor.service -f

# 查看最近的日志
journalctl -u device-monitor.service -n 50

# 查看启动脚本日志
tail -f /var/log/device_monitor.log
```

### 禁用开机自启
```bash
systemctl disable device-monitor.service
```

## 自动重启功能

服务配置了以下自动重启功能：
- `Restart=always`: 服务异常退出时自动重启
- `RestartSec=10`: 重启前等待10秒
- `StartLimitInterval=300`: 5分钟内
- `StartLimitBurst=5`: 最多重启5次

如果服务在5分钟内重启超过5次，systemd会停止尝试重启，需要手动干预。

## 防火墙设置

如果服务器启用了防火墙，需要开放端口：
```bash
# CentOS/RHEL (firewalld)
firewall-cmd --permanent --add-port=48085/tcp
firewall-cmd --permanent --add-port=5000/tcp
firewall-cmd --reload

# Ubuntu/Debian (ufw)
ufw allow 48085/tcp
ufw allow 5000/tcp
```

## 访问服务

- TCP服务器端口: 48085
- Web监控界面: http://服务器IP:5000

## 故障排除

### 1. 服务无法启动
```bash
# 检查详细错误信息
journalctl -u device-monitor.service -n 20

# 检查脚本日志
tail -20 /var/log/device_monitor.log
```

### 2. 端口占用
```bash
# 检查端口使用情况
netstat -tlnp | grep 48085
netstat -tlnp | grep 5000

# 如果端口被占用，可以杀死占用进程
kill -9 <进程ID>
```

### 3. 虚拟环境问题
```bash
# 重新创建虚拟环境
cd /root/device_monitor
rm -rf venv
python3 -m venv venv
source venv/bin/activate
pip install flask
```

### 4. 权限问题
```bash
# 确保文件权限正确
chown -R root:root /root/device_monitor
chmod +x /root/device_monitor/start_server.sh
```

## 监控建议

1. 定期检查日志文件大小，防止日志文件过大
2. 可以配置logrotate来自动轮转日志
3. 监控服务器资源使用情况
4. 定期备份配置文件和重要数据
