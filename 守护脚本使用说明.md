# 设备监控服务器守护脚本使用说明

## 快速部署

### 1. 上传脚本到服务器
将 `monitor_daemon.sh` 和 `auto_start.sh` 上传到 `/root/device_monitor/` 目录

### 2. 设置脚本权限
```bash
cd /root/device_monitor
chmod +x monitor_daemon.sh
chmod +x auto_start.sh
```

### 3. 启动守护脚本
```bash
# 方法A：前台运行（测试用）
./monitor_daemon.sh daemon

# 方法B：后台运行（推荐）
cd /root/device_monitor
nohup ./monitor_daemon.sh daemon > /var/log/daemon_startup.log 2>&1 &
```

## 守护脚本功能

### 自动监控功能
- ✅ 每30秒检查服务器进程是否存在
- ✅ 检查TCP端口48085和Web端口5000是否正常监听
- ✅ 自动重启异常停止的服务器
- ✅ 详细的日志记录

### 手动控制命令
```bash
# 启动服务器
./monitor_daemon.sh start

# 停止服务器
./monitor_daemon.sh stop

# 重启服务器
./monitor_daemon.sh restart

# 查看状态
./monitor_daemon.sh status

# 守护模式（持续监控）
./monitor_daemon.sh daemon
```

## 开机自启设置

### 方法1：使用crontab（推荐）
```bash
# 编辑crontab
crontab -e

# 添加以下行
@reboot /root/device_monitor/auto_start.sh

# 或者直接添加守护脚本
@reboot sleep 30 && cd /root/device_monitor && nohup ./monitor_daemon.sh daemon > /var/log/daemon_startup.log 2>&1 &
```

### 方法2：使用rc.local
```bash
# 编辑rc.local
vim /etc/rc.local

# 在exit 0之前添加
/root/device_monitor/auto_start.sh

# 确保rc.local有执行权限
chmod +x /etc/rc.local
```

## 日志查看

### 守护脚本日志
```bash
# 查看守护脚本日志
tail -f /var/log/device_monitor_daemon.log

# 查看最近50行日志
tail -50 /var/log/device_monitor_daemon.log
```

### 服务器运行日志
```bash
# 查看服务器运行日志
tail -f /root/device_monitor/server.log

# 查看启动日志
tail -f /var/log/daemon_startup.log
```

## 常用操作

### 1. 完整启动流程
```bash
cd /root/device_monitor

# 启动守护脚本（后台运行）
nohup ./monitor_daemon.sh daemon > /var/log/daemon_startup.log 2>&1 &

# 检查是否启动成功
ps aux | grep monitor_daemon
```

### 2. 查看运行状态
```bash
# 查看守护脚本状态
ps aux | grep monitor_daemon

# 查看服务器状态
./monitor_daemon.sh status

# 查看端口监听情况
netstat -tlnp | grep -E "48085|5000"
```

### 3. 停止所有服务
```bash
# 停止守护脚本
pkill -f monitor_daemon.sh

# 停止服务器
./monitor_daemon.sh stop

# 或者强制停止所有相关进程
pkill -f device_tcp_server.py
```

### 4. 重启所有服务
```bash
# 停止所有服务
pkill -f monitor_daemon.sh
pkill -f device_tcp_server.py

# 重新启动守护脚本
nohup ./monitor_daemon.sh daemon > /var/log/daemon_startup.log 2>&1 &
```

## 故障排除

### 1. 守护脚本无法启动
```bash
# 检查文件权限
ls -la /root/device_monitor/monitor_daemon.sh

# 检查错误日志
tail -20 /var/log/daemon_startup.log
```

### 2. 服务器频繁重启
```bash
# 查看守护脚本日志
tail -50 /var/log/device_monitor_daemon.log

# 查看服务器错误日志
tail -50 /root/device_monitor/server.log
```

### 3. 端口被占用
```bash
# 查看端口占用情况
netstat -tlnp | grep -E "48085|5000"

# 杀死占用端口的进程
kill -9 <进程ID>
```

## 监控配置调整

如果需要调整监控间隔，编辑 `monitor_daemon.sh` 文件：
```bash
# 修改检查间隔（默认30秒）
CHECK_INTERVAL=30
```

## 推荐的生产环境配置

1. **使用crontab开机自启**（最可靠）
2. **后台运行守护脚本**
3. **定期检查日志文件大小**
4. **配置日志轮转**（可选）

```bash
# 设置crontab开机自启
crontab -e
# 添加：@reboot sleep 30 && cd /root/device_monitor && nohup ./monitor_daemon.sh daemon > /var/log/daemon_startup.log 2>&1 &

# 启动守护脚本
cd /root/device_monitor
nohup ./monitor_daemon.sh daemon > /var/log/daemon_startup.log 2>&1 &
```

这样配置后，您的设备监控服务器就具备了：
- ✅ 自动重启异常停止的服务
- ✅ 开机自动启动
- ✅ 详细的监控日志
- ✅ 端口状态检查
- ✅ 手动控制功能
